class UserProfile < ApplicationRecord
  belongs_to :user

  scope :with_connections_for, ->(current_user_id) {
      # Absolutely simple approach - just get all user profiles
      # Handle duplicates in controller if needed
      includes(:user)
      .order('users.connections_count DESC')
    }

  scope :without_connections_for, ->(current_user_id) {
      # Simple approach: get all users who don't have connections
      connected_user_ids = NetworkConnection.where(
        '(inviter_id = ?) OR (invitee_id = ?)', current_user_id, current_user_id
      ).pluck(
        Arel.sql("CASE WHEN inviter_id = #{current_user_id} THEN invitee_id ELSE inviter_id END")
      ).uniq
      
      includes(:user)
      .where.not(user_id: connected_user_ids)
      .order('users.connections_count DESC')
  }

  validates :phone, phone: { possible: true, allow_blank: true }
  validate :email_cannot_change, on: :update

  def formatted_phone
    phone.present? ? Phonelib.parse(phone).full_international : ""
  end

  def connected_with?(current_user_id)
    return false unless current_user_id
    return true if current_user_id == user_id 

    NetworkConnection.where(inviter: [current_user_id, user_id])
                    .where(invitee: [current_user_id, user_id])
                    .exists?
  end

  def visible_attributes_for(current_user_id)
    if connected_with?(current_user_id)
      attributes.symbolize_keys
    else
      attributes.symbolize_keys.except(:email, :phone)
    end
  end


  def as_json(options = {})
    if options[:current_user_id] && connected_with?(options[:current_user_id])
      super(options)
    else
      super(options.merge(except: [:email, :phone]))
    end
  end

  def required_fields_present?
    first_name.present? && last_name.present? && city.present? && country.present?
  end

  def update_completion_status
    update(profile_completed: required_fields_present?)
  end

  # Helper methods for connection information
  def connection_exists(current_user_id = nil)
    return nil unless current_user_id
    
    NetworkConnection.where(
      '(inviter_id = ? AND invitee_id = ?) OR (invitee_id = ? AND inviter_id = ?)',
      current_user_id, user_id, current_user_id, user_id
    ).first&.id
  end

  def connection_status(current_user_id = nil)
    return nil unless current_user_id
    
    ConnectionRequest.where(
      inviter_id: current_user_id,
      invitee_id: user_id
    ).first&.status
  end

  
  private

  def email_cannot_change
    if email_changed? && self.persisted?
      errors.add(:email, "cannot be changed")
    end
  end

end
