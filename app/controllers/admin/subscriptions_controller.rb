# ABOUTME: Admin controller for managing user subscriptions and tier assignments  
# ABOUTME: Provides admin interface for viewing users by tier and manually updating subscription status
class Admin::SubscriptionsController < Admin::BaseController
  
  def index
    users_query = User.includes(:user_profile).order(:subscription_tier, :subscription_expires_at)
    @pagy, @users = pagy(users_query, limit: 25)
    @tier_stats = {
      free: User.tier_free.count,
      premium: User.tier_premium.count,
      beta: User.tier_beta.count
    }
  end
  
  def update_tier
    @user = User.find(params[:id])
    
    if @user.update(tier_params)
      redirect_to admin_subscriptions_path, notice: "Tier updated successfully for #{@user.email}"
    else
      redirect_to admin_subscriptions_path, alert: "Failed to update tier: #{@user.errors.full_messages.join(', ')}"
    end
  end
  
  private
  
  def tier_params
    params.require(:user).permit(:subscription_tier, :subscription_expires_at)
  end
end