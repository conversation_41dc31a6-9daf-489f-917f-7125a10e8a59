class UserProfilesController < ApplicationController
  before_action :authenticate_user! 
  before_action :set_user_profile, only: %i[ edit update ]

  # GET /user_profile
  def show
    @user_profile = UserProfile.find(params[:id])
    
    #not working fuckingactionpolicyagain
    #@can_view_contact_details = allowed_to?(:view_contact_details?, @user_profile)
    
    @is_connected = @user_profile.connected_with?(current_user.id)
    #@visible_profile_data = @user_profile.visible_attributes_for(current_user.id)
  end

  # GET /user_profile/edit
  def edit
  end

  def update
    if @user_profile.update(user_profile_params)
      # For first time users who just completed their profile, redirect to projects
      if current_user.sign_in_count == 1 && @user_profile.required_fields_present?
        redirect_to projects_path,
          notice: t('user_profiles.edit.success',
          default: "User profile was successfully updated."),
          status: :see_other
      else
        # For subsequent updates, stay on the edit page
        redirect_to edit_user_profile_path(@user_profile),
          notice: t('user_profiles.edit.success',
          default: "User profile was successfully updated."),
          status: :see_other
      end
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def set_language
    new_locale = params[:locale_code].to_s.strip.to_sym
    
    I18n.locale = new_locale
    current_user.user_profile.update(default_language: new_locale.to_s)
    
    redirect_back(fallback_location: root_path)
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_user_profile
      @user_profile = current_user.user_profile
    end

    # Only allow a list of trusted parameters through.
    def user_profile_params
      params.require(:user_profile).permit(:first_name, :last_name, :email, :bio, :phone, :city, :country)
    end
end
