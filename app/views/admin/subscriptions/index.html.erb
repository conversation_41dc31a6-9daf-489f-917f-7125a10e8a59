<% content_for :title, "Subscription Management" %>

<div class="admin-container">
  <div class="admin-header">
    <h1>Subscription Management</h1>
    <%= link_to "Referral Codes", admin_referral_codes_path, class: "admin-btn-secondary" %>
  </div>

  <!-- Tier Statistics -->
  <div class="admin-status-summary">
    <div class="admin-status-card admin-status-free">
      <h3><%= @tier_stats[:free] %></h3>
      <p>Free Users</p>
    </div>
    <div class="admin-status-card admin-status-premium">
      <h3><%= @tier_stats[:premium] %></h3>
      <p>Premium Users</p>
    </div>
    <div class="admin-status-card admin-status-beta">
      <h3><%= @tier_stats[:beta] %></h3>
      <p>Beta Users</p>
    </div>
  </div>

  <!-- Users Table -->
  <div class="admin-card">
    <div class="admin-card-header">
      <h2>All Users</h2>
    </div>
    <div class="admin-card-body">
      <div class="admin-table-responsive">
        <table class="admin-table table-striped">
              <thead>
                <tr>
                  <th>Email</th>
                  <th>Name</th>
                  <th>Current Tier</th>
                  <th>Expires At</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <% @users.each do |user| %>
                  <tr>
                    <td><%= user.email %></td>
                    <td>
                      <% if user.user_profile %>
                        <%= user.user_profile.first_name %> <%= user.user_profile.last_name %>
                      <% else %>
                        <em>No profile</em>
                      <% end %>
                    </td>
                    <td>
                      <span class="admin-tier-badge admin-tier-<%= user.subscription_tier %>">
                        <%= user.subscription_tier.humanize %>
                      </span>
                    </td>
                    <td>
                      <% if user.subscription_expires_at %>
                        <%= user.subscription_expires_at.strftime("%B %d, %Y") %>
                        <% if user.subscription_expires_at < Time.current %>
                          <small class="text-danger">(Expired)</small>
                        <% end %>
                      <% else %>
                        <span class="text-muted">Never</span>
                      <% end %>
                    </td>
                    <td>
                      <% if user.active_subscription? %>
                        <span class="admin-status-badge admin-status-active">Active</span>
                      <% else %>
                        <span class="admin-status-badge admin-status-inactive">Inactive</span>
                      <% end %>
                    </td>
                    <td>
                      <div class="admin-btn-group">
                        <button type="button" class="admin-btn-outline-primary admin-btn-sm" data-toggle="modal" data-target="#updateTierModal<%= user.id %>">
                          Update Tier
                        </button>
                      </div>
                    </td>
                  </tr>

                  <!-- Update Tier Modal -->
                  <div class="modal fade" id="updateTierModal<%= user.id %>" tabindex="-1">
                    <div class="modal-dialog">
                      <div class="modal-content">
                        <%= form_with url: update_tier_admin_subscription_path(user), method: :patch, local: true do |form| %>
                          <div class="modal-header">
                            <h5 class="modal-title">Update Tier for <%= user.email %></h5>
                            <button type="button" class="close" data-dismiss="modal">
                              <span>&times;</span>
                            </button>
                          </div>
                          <div class="modal-body">
                            <div class="form-group">
                              <%= form.label :subscription_tier, "Subscription Tier" %>
                              <%= form.select :subscription_tier, 
                                  options_for_select([
                                    ['Free', 'free'],
                                    ['Premium', 'premium'],
                                    ['Beta', 'beta']
                                  ], user.subscription_tier), 
                                  {}, 
                                  { class: "form-control" } %>
                            </div>
                            <div class="form-group">
                              <%= form.label :subscription_expires_at, "Expires At (leave blank for Beta/permanent)" %>
                              <%= form.datetime_local_field :subscription_expires_at, 
                                  value: user.subscription_expires_at&.strftime("%Y-%m-%dT%H:%M"),
                                  class: "form-control" %>
                              <small class="form-text text-muted">
                                Only applies to Premium tier. Beta users never expire.
                              </small>
                            </div>
                          </div>
                          <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <%= form.submit "Update Tier", class: "btn btn-primary" %>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  </div>
                <% end %>
              </tbody>
            </table>
          </div>

        <!-- Pagination -->  
        <% if @pagy.pages > 1 %>
          <div class="admin-d-flex admin-justify-content-center">
            <%== pagy_nav(@pagy) %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<% content_for :page_scripts do %>
<script>
// Auto-clear expiration date when selecting Free or Beta tier
document.querySelectorAll('select[name="user[subscription_tier]"]').forEach(function(select) {
  select.addEventListener('change', function() {
    const expiresField = this.closest('.modal-content').querySelector('input[name="user[subscription_expires_at]"]');
    if (this.value === 'free' || this.value === 'beta') {
      expiresField.value = '';
      expiresField.disabled = true;
    } else {
      expiresField.disabled = false;
    }
  });
});
</script>
<% end %>