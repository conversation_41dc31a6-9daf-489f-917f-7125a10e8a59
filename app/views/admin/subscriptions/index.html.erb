<% content_for :title, "Subscription Management" %>

<div class="admin-container">
  <div class="admin-header">
    <h1>Subscription Management</h1>
    <%= link_to "Referral Codes", admin_referral_codes_path, class: "button button-secondary" %>
  </div>

  <!-- Tier Statistics -->
  <div class="flex g1 items-center mb-1" style="font-size: 14px; color: #666;">
    <span><strong><%= @tier_stats[:free] %></strong> Free</span>
    <span>•</span>
    <span><strong><%= @tier_stats[:premium] %></strong> Premium</span>
    <span>•</span>
    <span><strong><%= @tier_stats[:beta] %></strong> Beta</span>
  </div>

  <!-- Users List -->
  <div class="list-box">
    <h2>All Users</h2>

    <div style="overflow-x: auto;">
      <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
        <thead>
          <tr style="border-bottom: 2px solid #e5e7eb; background: #f8f9fa;">
            <th style="padding: 8px; text-align: left; font-weight: 600;">User</th>
            <th style="padding: 8px; text-align: left; font-weight: 600;">Tier</th>
            <th style="padding: 8px; text-align: left; font-weight: 600;">Status</th>
            <th style="padding: 8px; text-align: left; font-weight: 600;">Expires</th>
            <th style="padding: 8px; text-align: center; font-weight: 600;">Actions</th>
          </tr>
        </thead>
        <tbody>
          <% @users.each do |user| %>
            <tr style="border-bottom: 1px solid #e5e7eb;">
              <td style="padding: 8px;">
                <div>
                  <strong><%= user.email %></strong>
                  <% if user.user_profile %>
                    <br><small style="color: #666;"><%= user.user_profile.first_name %> <%= user.user_profile.last_name %></small>
                  <% end %>
                </div>
              </td>
              <td style="padding: 8px;">
                <span class="tag <%= user.subscription_tier == 'premium' ? 'attention' : user.subscription_tier == 'beta' ? 'ready' : 'info' %>">
                  <%= user.subscription_tier.humanize %>
                </span>
              </td>
              <td style="padding: 8px;">
                <% if user.active_subscription? %>
                  <span class="tag ready">Active</span>
                <% else %>
                  <span class="tag inactive">Inactive</span>
                <% end %>
              </td>
              <td style="padding: 8px;">
                <% if user.subscription_expires_at %>
                  <%= user.subscription_expires_at.strftime("%b %d, %Y") %>
                  <% if user.subscription_expires_at < Time.current %>
                    <br><small class="text-red">(Expired)</small>
                  <% end %>
                <% else %>
                  <small style="color: #666;">Never</small>
                <% end %>
              </td>
              <td style="padding: 8px; text-align: center;">
                <button type="button" class="text-link" data-toggle="modal" data-target="#updateTierModal<%= user.id %>">
                  Update
                </button>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>

                  <!-- Update Tier Modal -->
                  <div class="modal fade" id="updateTierModal<%= user.id %>" tabindex="-1">
                    <div class="modal-dialog">
                      <div class="modal-content">
                        <%= form_with url: update_tier_admin_subscription_path(user), method: :patch, local: true do |form| %>
                          <div class="modal-header">
                            <h5 class="modal-title">Update Tier for <%= user.email %></h5>
                            <button type="button" class="close" data-dismiss="modal">
                              <span>&times;</span>
                            </button>
                          </div>
                          <div class="modal-body">
                            <div class="field">
                              <%= form.label :subscription_tier, "Subscription Tier" %>
                              <%= form.select :subscription_tier,
                                  options_for_select([
                                    ['Free', 'free'],
                                    ['Premium', 'premium'],
                                    ['Beta', 'beta']
                                  ], user.subscription_tier),
                                  {},
                                  { class: "form-select" } %>
                            </div>
                            <div class="field">
                              <%= form.label :subscription_expires_at, "Expires At (leave blank for Beta/permanent)" %>
                              <%= form.datetime_local_field :subscription_expires_at,
                                  value: user.subscription_expires_at&.strftime("%Y-%m-%dT%H:%M"),
                                  class: "form-input" %>
                              <small style="color: #666; font-size: 0.9em;">
                                Only applies to Premium tier. Beta users never expire.
                              </small>
                            </div>
                          </div>
                          <div class="modal-footer">
                            <button type="button" class="button-outline" data-dismiss="modal">Cancel</button>
                            <%= form.submit "Update Tier", class: "button button-primary" %>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  </div>
      <% end %>
    </div>

    <!-- Pagination -->
    <% if @pagy.pages > 1 %>
      <div class="flex justify-center mt-1">
        <%== pagy_nav(@pagy) %>
      </div>
    <% end %>
  </div>
</div>

<% content_for :page_scripts do %>
<script>
// Auto-clear expiration date when selecting Free or Beta tier
document.querySelectorAll('select[name="user[subscription_tier]"]').forEach(function(select) {
  select.addEventListener('change', function() {
    const expiresField = this.closest('.modal-content').querySelector('input[name="user[subscription_expires_at]"]');
    if (this.value === 'free' || this.value === 'beta') {
      expiresField.value = '';
      expiresField.disabled = true;
    } else {
      expiresField.disabled = false;
    }
  });
});
</script>
<% end %>