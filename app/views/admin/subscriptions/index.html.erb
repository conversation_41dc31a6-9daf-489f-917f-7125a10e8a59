<% content_for :title, "Subscription Management" %>

<div class="admin-container">
  <div class="admin-header">
    <h1>Subscription Management</h1>
    <%= link_to "Referral Codes", admin_referral_codes_path, class: "button button-secondary" %>
  </div>

  <!-- Tier Statistics -->
  <div class="flex g2 mb-2">
    <div class="list-box text-c">
      <h3><%= @tier_stats[:free] %></h3>
      <p>Free Users</p>
    </div>
    <div class="list-box text-c">
      <h3><%= @tier_stats[:premium] %></h3>
      <p>Premium Users</p>
    </div>
    <div class="list-box text-c">
      <h3><%= @tier_stats[:beta] %></h3>
      <p>Beta Users</p>
    </div>
  </div>

  <!-- Users List -->
  <div class="list-box">
    <h2>All Users</h2>

    <div class="list">
      <% @users.each do |user| %>
        <div class="list-item">
          <div class="flex col g1">
            <div class="flex space items-center">
              <div class="flex col">
                <strong><%= user.email %></strong>
                <div>
                  <% if user.user_profile %>
                    <%= user.user_profile.first_name %> <%= user.user_profile.last_name %>
                  <% else %>
                    <em>No profile</em>
                  <% end %>
                </div>
              </div>
              <div class="flex g1 items-center">
                <span class="tag <%= user.subscription_tier == 'premium' ? 'attention' : user.subscription_tier == 'beta' ? 'ready' : 'info' %>">
                  <%= user.subscription_tier.humanize %>
                </span>
                <% if user.active_subscription? %>
                  <span class="tag ready">Active</span>
                <% else %>
                  <span class="tag inactive">Inactive</span>
                <% end %>
              </div>
            </div>
            <div class="flex space items-center">
              <div>
                <% if user.subscription_expires_at %>
                  Expires: <%= user.subscription_expires_at.strftime("%B %d, %Y") %>
                  <% if user.subscription_expires_at < Time.current %>
                    <span class="text-red">(Expired)</span>
                  <% end %>
                <% else %>
                  <span style="color: #666;">Never expires</span>
                <% end %>
              </div>
              <div>
                <button type="button" class="button-outline" data-toggle="modal" data-target="#updateTierModal<%= user.id %>">
                  Update Tier
                </button>
              </div>
            </div>
          </div>
        </div>

                  <!-- Update Tier Modal -->
                  <div class="modal fade" id="updateTierModal<%= user.id %>" tabindex="-1">
                    <div class="modal-dialog">
                      <div class="modal-content">
                        <%= form_with url: update_tier_admin_subscription_path(user), method: :patch, local: true do |form| %>
                          <div class="modal-header">
                            <h5 class="modal-title">Update Tier for <%= user.email %></h5>
                            <button type="button" class="close" data-dismiss="modal">
                              <span>&times;</span>
                            </button>
                          </div>
                          <div class="modal-body">
                            <div class="field">
                              <%= form.label :subscription_tier, "Subscription Tier" %>
                              <%= form.select :subscription_tier,
                                  options_for_select([
                                    ['Free', 'free'],
                                    ['Premium', 'premium'],
                                    ['Beta', 'beta']
                                  ], user.subscription_tier),
                                  {},
                                  { class: "form-select" } %>
                            </div>
                            <div class="field">
                              <%= form.label :subscription_expires_at, "Expires At (leave blank for Beta/permanent)" %>
                              <%= form.datetime_local_field :subscription_expires_at,
                                  value: user.subscription_expires_at&.strftime("%Y-%m-%dT%H:%M"),
                                  class: "form-input" %>
                              <small style="color: #666; font-size: 0.9em;">
                                Only applies to Premium tier. Beta users never expire.
                              </small>
                            </div>
                          </div>
                          <div class="modal-footer">
                            <button type="button" class="button-outline" data-dismiss="modal">Cancel</button>
                            <%= form.submit "Update Tier", class: "button button-primary" %>
                          </div>
                        <% end %>
                      </div>
                    </div>
                  </div>
      <% end %>
    </div>

    <!-- Pagination -->
    <% if @pagy.pages > 1 %>
      <div class="flex justify-center mt-1">
        <%== pagy_nav(@pagy) %>
      </div>
    <% end %>
  </div>
</div>

<% content_for :page_scripts do %>
<script>
// Auto-clear expiration date when selecting Free or Beta tier
document.querySelectorAll('select[name="user[subscription_tier]"]').forEach(function(select) {
  select.addEventListener('change', function() {
    const expiresField = this.closest('.modal-content').querySelector('input[name="user[subscription_expires_at]"]');
    if (this.value === 'free' || this.value === 'beta') {
      expiresField.value = '';
      expiresField.disabled = true;
    } else {
      expiresField.disabled = false;
    }
  });
});
</script>
<% end %>