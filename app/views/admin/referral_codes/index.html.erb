<% content_for :title, "Referral Code Management" %>

<div class="admin-container">
  <div class="admin-header">
    <h1>Referral Code Management</h1>
    <%= link_to "Subscriptions", admin_subscriptions_path, class: "admin-btn-secondary" %>
  </div>

  <!-- Create New Code -->
  <h2>Create New Referral Code</h2>
  <%= form_with model: [:admin, @new_code], local: true, html: { class: "admin-form" } do |form| %>
    <% if @new_code.errors.any? %>
      <div class="admin-error-box">
        <strong>Please fix the following errors:</strong>
        <ul>
          <% @new_code.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="admin-form-row">
      <div class="admin-form-group">
        <%= form.label :code %>
        <%= form.text_field :code, class: "admin-input", placeholder: "e.g., WELCOME2024" %>
      </div>
      <div class="admin-form-group">
        <%= form.label :tier_upgrade_to, "Upgrade To" %>
        <%= form.select :tier_upgrade_to, 
            options_for_select([
              ['Premium', 'premium'],
              ['Beta', 'beta']
            ]), 
            {}, 
            { class: "admin-select" } %>
      </div>
      <div class="admin-form-group">
        <%= form.label :duration_months, "Duration (months)" %>
        <%= form.number_field :duration_months, class: "admin-input", value: 1, min: 1 %>
      </div>
      <div class="admin-form-group">
        <%= form.label :max_uses, "Max Uses" %>
        <%= form.number_field :max_uses, class: "admin-input", value: 1, min: 1 %>
      </div>
      <div class="admin-form-group">
        <%= form.label :expires_at, "Expires At" %>
        <%= form.datetime_local_field :expires_at, 
            class: "admin-input",
            value: 1.month.from_now.strftime("%Y-%m-%dT%H:%M") %>
      </div>
    </div>
    
    <div class="admin-form-group">
      <%= form.label :description %>
      <%= form.text_area :description, class: "admin-textarea", rows: 2, placeholder: "Optional description for this code" %>
    </div>
    
    <%= form.submit "Create Code", class: "admin-btn-primary" %>
  <% end %>

  <!-- Existing Codes -->
  <h2 style="margin-top: 40px;">Existing Referral Codes</h2>
  
  <div class="admin-flex-table">
    <div class="admin-flex-table-header">
      <div class="admin-flex-table-cell admin-col-code">Code</div>
      <div class="admin-flex-table-cell admin-col-status">Status</div>
      <div class="admin-flex-table-cell admin-col-tier">Upgrade To</div>
      <div class="admin-flex-table-cell admin-col-duration">Duration</div>
      <div class="admin-flex-table-cell admin-col-usage">Usage</div>
      <div class="admin-flex-table-cell admin-col-expires">Expires</div>
      <div class="admin-flex-table-cell admin-col-email">Created By</div>
      <div class="admin-flex-table-cell admin-col-description">Description</div>
      <div class="admin-flex-table-cell admin-col-actions">Actions</div>
    </div>

    <% @codes.each do |code| %>
      <div class="admin-flex-table-row <%= 'admin-table-warning' if code.expires_at&.past? %>">
        <div class="admin-flex-table-cell admin-col-code">
          <strong><%= code.code %></strong>
        </div>
        <div class="admin-flex-table-cell admin-col-status">
          <span class="admin-status-badge admin-status-<%= code.status %>">
            <%= code.status.humanize %>
          </span>
        </div>
        <div class="admin-flex-table-cell admin-col-tier">
          <span class="admin-tier-badge admin-tier-<%= code.tier_upgrade_to %>">
            <%= code.tier_upgrade_to.humanize %>
          </span>
        </div>
        <div class="admin-flex-table-cell admin-col-duration">
          <%= pluralize(code.duration_months, 'month') %>
        </div>
        <div class="admin-flex-table-cell admin-col-usage">
          <span class="<%= 'admin-text-danger' if code.current_uses >= code.max_uses %>">
            <%= code.current_uses %> / <%= code.max_uses %>
          </span>
        </div>
        <div class="admin-flex-table-cell admin-col-expires">
          <% if code.expires_at %>
            <%= code.expires_at.strftime("%b %d, %Y") %>
            <% if code.expires_at.past? %>
              <small class="admin-text-danger">(Expired)</small>
            <% end %>
          <% else %>
            <span class="admin-text-muted">Never</span>
          <% end %>
        </div>
        <div class="admin-flex-table-cell admin-col-email">
          <%= code.created_by.email %>
        </div>
        <div class="admin-flex-table-cell admin-col-description">
          <% if code.description.present? %>
            <%= truncate(code.description, length: 50) %>
          <% else %>
            <span class="admin-text-muted">No description</span>
          <% end %>
        </div>
        <div class="admin-flex-table-cell admin-col-actions">
          <div class="admin-btn-group">
            <%= link_to "View", admin_referral_code_path(code), class: "admin-btn-outline-info admin-btn-sm" %>
            <% if code.active? %>
              <%= link_to "Disable", admin_referral_code_path(code), 
                  method: :patch, 
                  params: { referral_code: { status: 'disabled' } },
                  class: "admin-btn-outline-warning admin-btn-sm",
                  confirm: "Are you sure you want to disable this code?" %>
            <% end %>
            <%= link_to "Delete", admin_referral_code_path(code), 
                method: :delete, 
                class: "admin-btn-outline-danger admin-btn-sm",
                confirm: "Are you sure you want to delete this code?" %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Pagination -->
  <% if @pagy.pages > 1 %>
    <div style="text-align: center; margin-top: 20px;">
      <%== pagy_nav(@pagy) %>
    </div>
  <% end %>
</div>