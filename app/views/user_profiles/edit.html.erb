<div class="profile-edit">
  <div class="">

    <div class="flex flex-column g1 align-start">

      <h1><%= t('user_profiles.edit.title', default: 'Edit Profile') %></h1>

      <% if !@user_profile.last_name? || !@user_profile.first_name? || !@user_profile.city? || !@user_profile.country? %>
        <div class="cta-box-profile">
          <div class="w-1">
            <%= t('user_profiles.edit.personalize_experience', 
                default: 'Let’s personalize your experience. Tell us a bit about yourself to unlock amazing off-market deals.') %>
          </div>
        </div>
      <% end %>

      <%= form_with(model: @user_profile) do |form| %>
        <div class="flex g1">
          <div class="g1">
            <% if @user_profile.errors.any? %>
              <div style="color: red">
                <ul>
                  <% @user_profile.errors.each do |error| %>
                    <li><%= error.full_message %></li>
                  <% end %>
                </ul>
              </div>
            <% end %>

            <%# First Name / Last Name Row %>
            <div class="form-row">
              <div class="form-field-group">
                <%= form.label :first_name, t('models.user_profile.attributes.first_name') %>
                <%= form.text_field :first_name, required: true %>
              </div>
              <div class="form-field-group">
                <%= form.label :last_name, t('models.user_profile.attributes.last_name') %>
                <%= form.text_field :last_name, required: true %>
              </div>
            </div>

            <%# City / Country Row %>
            <div class="form-row">
              <div class="form-field-group">
                <%= form.label :city, t('models.user_profile.attributes.city') %>
                <%= form.text_field :city, required: true %>
              </div>
              <div class="form-field-group">
                <%= form.label :country, t('models.user_profile.attributes.country') %>
                <%= form.text_field :country, required: true %>
              </div>
            </div>

            <%# Bio Full Width %>
            <div class="form-row">
              <div class="form-field-full-width">
                <%= form.label :bio, t('models.user_profile.attributes.bio') %>
                <%= form.text_area :bio %>
              </div>
            </div>
            <%# Email / Phone Row %>
            <div class="form-row">
              <div class="form-field-group">
                <%= form.label :email, t('models.user_profile.attributes.email') %>
                <%= form.text_field :email, disabled: true, placeholder: t('user_profiles.edit.email_placeholder') %>
              </div>
              <div class="form-field-group">
                <%= form.label :phone, t('models.user_profile.attributes.phone') %>
                <%= form.text_field :phone,  placeholder: t('user_profiles.edit.phone_placeholder') %>
              </div>
            </div>


            <div class="actions flex g1">
              <div>
                <%= link_to t('user_profiles.edit.change_password', default: 'Change Password'), edit_user_registration_path %>
              </div>
              <div>
                <%= form.submit t('common.actions.save') %>
              </div>
            </div>
          </div>
        </div>

      <% end %>

    </div>

  </div>
</div>